import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gap/gap.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';

class FullscreenImageViewer extends StatefulWidget {
  final String imagePath;
  final String initialCaption;
  final VoidCallback onRetake;
  final Function(String caption) onUsePhoto;

  const FullscreenImageViewer({
    super.key,
    required this.imagePath,
    required this.initialCaption,
    required this.onRetake,
    required this.onUsePhoto,
  });

  @override
  State<FullscreenImageViewer> createState() => _FullscreenImageViewerState();
}

class _FullscreenImageViewerState extends State<FullscreenImageViewer> {
  late TextEditingController _captionController;
  late FocusNode _captionFocusNode;

  @override
  void initState() {
    super.initState();
    _captionController = TextEditingController(text: widget.initialCaption);
    _captionFocusNode = FocusNode();
  }

  @override
  void dispose() {
    _captionController.dispose();
    _captionFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.close, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        systemOverlayStyle: const SystemUiOverlayStyle(
          statusBarColor: Colors.black,
          statusBarIconBrightness: Brightness.light,
          systemNavigationBarColor: Colors.black,
          systemNavigationBarIconBrightness: Brightness.light,
        ),
      ),
      extendBodyBehindAppBar: true,
      body: Column(
        children: [
          // Image display area
          Expanded(
            child: Center(
              child: InteractiveViewer(
                minScale: 0.5,
                maxScale: 3.0,
                child: Image.file(
                  File(widget.imagePath),
                  fit: BoxFit.contain,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      padding: const EdgeInsets.all(20),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Icon(
                            Icons.error_outline,
                            color: Colors.white,
                            size: 48,
                          ),
                          const Gap(16),
                          Text(
                            'Failed to load image',
                            style: textTheme.montserratTitleExtraSmall.copyWith(
                              color: Colors.white,
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ),
            ),
          ),

          // Bottom UI section
          Container(
            padding: const EdgeInsets.all(20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Caption text field
                Container(
                  decoration: BoxDecoration(
                    // color: Colors.white.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: Colors.white.withValues(alpha: 0.3),
                      width: 1,
                    ),
                  ),
                  child: TextField(
                    controller: _captionController,
                    focusNode: _captionFocusNode,
                    style: textTheme.montserratTitleExtraSmall.copyWith(
                      color: Colors.white,
                    ),
                    decoration: InputDecoration(
                      isDense: true,
                      hintText: 'Add comment',
                      hintStyle: textTheme.montserratTableSmall.copyWith(
                        color: Colors.white.withValues(alpha: 0.7),
                      ),
                      border: InputBorder.none,
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 14,
                      ),
                      prefixIcon: Icon(
                        Icons.comment_outlined,
                        color: Colors.white.withValues(alpha: 0.7),
                        size: 20,
                      ),
                      fillColor: Colors.black,
                    ),
                    maxLines: 3,
                    minLines: 1,
                    textInputAction: TextInputAction.done,
                    onSubmitted: (_) => _captionFocusNode.unfocus(),
                  ),
                ),

                const Gap(8),

                // Action buttons
                Row(
                  children: [
                    // Retake button
                    TextButton(
                      child: Text(
                        'Retake',
                        style: textTheme.montserratTitleExtraSmall.copyWith(
                          color: Colors.white,
                        ),
                      ),
                      onPressed: () {
                        Navigator.pop(context);
                        widget.onRetake();
                      },
                    ),

                    const Spacer(),

                    // Use photo button
                    TextButton(
                      child: Text(
                        'Use Photo',
                        style: textTheme.montserratTitleExtraSmall.copyWith(
                          color: Colors.white,
                        ),
                      ),
                      onPressed: () {
                        final caption = _captionController.text.trim();
                        Navigator.pop(context);
                        widget.onUsePhoto(caption.isEmpty ? 'Photo' : caption);
                      },
                    ),
                  ],
                ),

                // Bottom safe area padding
                SizedBox(height: MediaQuery.of(context).padding.bottom),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
