import 'package:realm/realm.dart';
import 'package:storetrack_app/features/home/<USER>/models/calendar_info_model.dart';
import 'package:storetrack_app/features/home/<USER>/models/country_model.dart';
import 'package:storetrack_app/features/home/<USER>/models/misc_setting_model.dart';
import 'package:storetrack_app/features/home/<USER>/models/opened_doc_item_model.dart';
import 'package:storetrack_app/features/home/<USER>/models/pos_response_item_model.dart';
import 'package:storetrack_app/features/home/<USER>/models/profile_model.dart';
import 'package:storetrack_app/features/home/<USER>/models/history_model.dart';
import 'package:storetrack_app/features/home/<USER>/models/alert_model.dart';
import 'package:storetrack_app/features/home/<USER>/models/availability_model.dart';
import 'package:storetrack_app/features/home/<USER>/models/leave_model.dart';
import 'package:storetrack_app/features/home/<USER>/models/skills_model.dart';
import 'package:storetrack_app/features/home/<USER>/models/induction_model.dart';
import 'package:storetrack_app/features/home/<USER>/models/address_skill_validation_model.dart';
import 'package:storetrack_app/features/home/<USER>/models/state_model.dart';
import 'package:storetrack_app/shared/models/downloaded_file_model.dart';

import '../../features/home/<USER>/models/task_detail_model.dart';

class RealmDatabase {
  static RealmDatabase? _instance;
  late Realm _realm;

  RealmDatabase._() {
    final config = Configuration.local(
      [
        // TaskDetail related
        TaskDetailModel.schema,
        PhotoFolderModel.schema,
        SignatureFolderModel.schema,
        FormModel.schema,
        QuestionAnswerModel.schema,
        PosItemModel.schema,
        DocumentModel.schema,
        TaskalertModel.schema,
        TaskmemberModel.schema,
        FollowupTaskModel.schema,
        StocktakeModel.schema,
        PhotoModel.schema,
        SignatureModel.schema,
        QuestionModel.schema,
        QuestionPartModel.schema,
        MeasurementModel.schema,
        MeasurementOptionModel.schema,
        MeasurementConditionModel.schema,
        MeasurementValidationModel.schema,
        MeasurementPhototypesDeprecatedModel.schema,
        QuestionConditionModel.schema,
        PhotoTagsTModel.schema,
        CommentTypeModel.schema,
        FileElementModel.schema,
        // ResumePauseItem related
        ResumePauseItemModel.schema,
        // CalendarInfo related
        CalendarInfoModel.schema,
        // POS Response related
        PosResponseItemModel.schema,
        // Profile related
        ProfileModel.schema,
        // Misc Setting related
        MiscSettingModel.schema,
        // Cache models for emulation
        HistoryModel.schema,
        HistoryItemModel.schema,
        AlertModel.schema,
        AlertItemModel.schema,
        AvailabilityModel.schema,
        DayAvailabilityModel.schema,
        DaySpanModel.schema,
        LeaveModel.schema,
        LeaveItemModel.schema,
        SkillsModel.schema,
        SkillItemModel.schema,
        InductionModel.schema,
        InductionItemModel.schema,
        AddressSkillValidationModel.schema,
        // Downloaded File related
        DownloadedFileModel.schema,
        // Opened Doc related
        OpenedDocItemModel.schema,
        // Address related
        CountryModel.schema,
        StateModel.schema,
      ],
      schemaVersion:
          22, // Incremented due to PosResponseItemModel primary key change from int to ObjectId
    );
    _realm = Realm(config);
  }

  static RealmDatabase get instance {
    _instance ??= RealmDatabase._();
    return _instance!;
  }

  Realm get realm => _realm;

  void clearAllData() {
    _realm.write(() {
      _realm.deleteAll<TaskDetailModel>();
      _realm.deleteAll<PhotoFolderModel>();
      _realm.deleteAll<SignatureFolderModel>();
      _realm.deleteAll<FormModel>();
      _realm.deleteAll<QuestionAnswerModel>();
      _realm.deleteAll<PosItemModel>();
      _realm.deleteAll<DocumentModel>();
      _realm.deleteAll<TaskalertModel>();
      _realm.deleteAll<TaskmemberModel>();
      _realm.deleteAll<FollowupTaskModel>();
      _realm.deleteAll<StocktakeModel>();
      _realm.deleteAll<PhotoModel>();
      _realm.deleteAll<SignatureModel>();
      _realm.deleteAll<QuestionModel>();
      _realm.deleteAll<QuestionPartModel>();
      _realm.deleteAll<MeasurementModel>();
      _realm.deleteAll<MeasurementOptionModel>();
      _realm.deleteAll<MeasurementConditionModel>();
      _realm.deleteAll<MeasurementValidationModel>();
      _realm.deleteAll<PosResponseItemModel>();
      _realm.deleteAll<MeasurementPhototypesDeprecatedModel>();
      _realm.deleteAll<QuestionConditionModel>();
      _realm.deleteAll<PhotoTagsTModel>();
      _realm.deleteAll<CommentTypeModel>();
      _realm.deleteAll<FileElementModel>();
      _realm.deleteAll<ResumePauseItemModel>();
      _realm.deleteAll<CalendarInfoModel>();
      _realm.deleteAll<ProfileModel>();
      _realm.deleteAll<MiscSettingModel>();
      // Cache models for emulation
      _realm.deleteAll<HistoryModel>();
      _realm.deleteAll<AlertModel>();
      _realm.deleteAll<AvailabilityModel>();
      _realm.deleteAll<LeaveModel>();
      _realm.deleteAll<SkillsModel>();
      _realm.deleteAll<InductionModel>();
      _realm.deleteAll<AddressSkillValidationModel>();
      _realm.deleteAll<DownloadedFileModel>();
    });
  }

  void clearTaskDetailData() {
    _realm.write(() {
      _realm.deleteAll<TaskDetailModel>();
      _realm.deleteAll<DocumentModel>();
      _realm.deleteAll<FileElementModel>();
      _realm.deleteAll<FollowupTaskModel>();
      _realm.deleteAll<FormModel>();
      _realm.deleteAll<QuestionAnswerModel>();
      _realm.deleteAll<QuestionModel>();
      _realm.deleteAll<CommentTypeModel>();
      _realm.deleteAll<MeasurementModel>();
      _realm.deleteAll<MeasurementConditionModel>();
      _realm.deleteAll<MeasurementOptionModel>();
      _realm.deleteAll<MeasurementPhototypesDeprecatedModel>();
      _realm.deleteAll<MeasurementValidationModel>();
      _realm.deleteAll<PhotoModel>();
      _realm.deleteAll<PhotoTagsTModel>();
      _realm.deleteAll<QuestionConditionModel>();
      _realm.deleteAll<QuestionPartModel>();
      _realm.deleteAll<PhotoFolderModel>();
      _realm.deleteAll<PosItemModel>();
      _realm.deleteAll<SignatureFolderModel>();
      _realm.deleteAll<SignatureModel>();
      _realm.deleteAll<StocktakeModel>();
      _realm.deleteAll<TaskalertModel>();
      _realm.deleteAll<TaskmemberModel>();
      _realm.deleteAll<ResumePauseItemModel>();
    });
  }

  void close() {
    _realm.close();
  }
}
